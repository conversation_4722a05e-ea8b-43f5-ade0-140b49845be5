# GitHub Secrets and Variables Setup

This document outlines the required secrets and variables that need to be configured in your GitHub repository for the CI/CD pipeline to work properly.

## Repository Secrets

Navigate to your repository → Settings → Secrets and variables → Actions → Repository secrets

### Required Secrets

#### Supabase Configuration
- `STAGING_SUPABASE_URL` - Supabase project URL for staging environment
- `STAGING_SUPABASE_ANON_KEY` - Supabase anonymous key for staging environment
- `PROD_SUPABASE_URL` - Supabase project URL for production environment
- `PROD_SUPABASE_ANON_KEY` - Supabase anonymous key for production environment
- `SUPABASE_ACCESS_TOKEN` - Supabase access token for database migrations

#### Deployment URLs
- `STAGING_ADMIN_URL` - URL for staging admin panel
- `STAGING_WHOLESALE_URL` - URL for staging wholesale portal
- `PROD_ADMIN_URL` - URL for production admin panel
- `PROD_WHOLESALE_URL` - URL for production wholesale portal

#### Netlify Configuration
- `NETLIFY_AUTH_TOKEN` - Netlify authentication token for deployments

#### Railway Configuration (for AI Backend)
- `RAILWAY_TOKEN` - Railway authentication token
- `RAILWAY_PROJECT_ID` - Railway project ID

#### Database Backup (Optional)
- `PROD_DATABASE_URL` - Production database connection URL
- `DB_PASSWORD` - Database password for backups
- `AWS_ACCESS_KEY_ID` - AWS access key for S3 backup storage
- `AWS_SECRET_ACCESS_KEY` - AWS secret key for S3 backup storage

#### Notifications (Optional)
- `SLACK_WEBHOOK_URL` - Slack webhook URL for deployment notifications

#### Security Scanning (Optional)
- `SNYK_TOKEN` - Snyk authentication token for vulnerability scanning

#### Code Coverage (Optional)
- `CODECOV_TOKEN` - Codecov token for uploading test coverage reports

#### Mobile Development (Optional)
- `EXPO_TOKEN` - Expo authentication token for EAS builds

## Repository Variables

Navigate to your repository → Settings → Secrets and variables → Actions → Variables

### Required Variables

#### Netlify Site IDs
- `NETLIFY_ADMIN_SITE_ID` - Netlify site ID for admin panel
- `NETLIFY_WHOLESALE_SITE_ID` - Netlify site ID for wholesale portal

#### Railway Configuration
- `RAILWAY_PROJECT_ID` - Railway project ID (can also be set as secret)

#### Feature Toggles
- `SLACK_NOTIFICATIONS_ENABLED` - Set to 'true' to enable Slack notifications
- `BACKUP_ENABLED` - Set to 'true' to enable database backups
- `AWS_BACKUP_ENABLED` - Set to 'true' to enable AWS S3 backup uploads
- `SUPABASE_PROJECT_ID` - Supabase project ID for migrations
- `SNYK_ENABLED` - Set to 'true' to enable Snyk security scanning
- `CODECOV_ENABLED` - Set to 'true' to enable Codecov coverage uploads
- `EXPO_BUILD_ENABLED` - Set to 'true' to enable Expo EAS builds

#### Backup Configuration
- `BACKUP_BUCKET` - S3 bucket name for database backups

## Environment Setup

### Staging Environment
Create a staging environment in your repository settings with the following protection rules:
- Required reviewers: 0 (for automatic deployments)
- Wait timer: 0 minutes
- Deployment branches: `develop` branch only

### Production Environment
Create a production environment in your repository settings with the following protection rules:
- Required reviewers: 1+ (recommended for production safety)
- Wait timer: 0 minutes (or add delay if needed)
- Deployment branches: `main` branch only

## How to Obtain These Values

### Supabase
1. Go to your Supabase project dashboard
2. Navigate to Settings → API
3. Copy the Project URL and anon/public key
4. For access token: Settings → Access Tokens → Create new token

### Netlify
1. Go to Netlify dashboard
2. For auth token: User settings → Applications → Personal access tokens
3. For site IDs: Site settings → General → Site details → Site ID

### Railway
1. Go to Railway dashboard
2. For token: Account settings → Tokens → Create token
3. For project ID: Project settings → General → Project ID

### AWS S3 (for backups)
1. Create an IAM user with S3 access
2. Generate access keys for the user
3. Create an S3 bucket for backups

### Slack (for notifications)
1. Create a Slack app in your workspace
2. Enable incoming webhooks
3. Create a webhook URL for your desired channel

### Snyk (for security scanning)
1. Create a free Snyk account at https://snyk.io
2. Go to Account settings → General → Auth Token
3. Generate and copy the API token

### Codecov (for code coverage)
1. Create a free Codecov account at https://codecov.io
2. Connect your GitHub repository
3. Copy the repository upload token from settings

### Expo (for mobile builds)
1. Create an Expo account at https://expo.dev
2. Go to Access tokens in your account settings
3. Create a new token with appropriate permissions

## Security Notes

- Never commit secrets to your repository
- Use environment-specific secrets (staging vs production)
- Regularly rotate access tokens and keys
- Limit permissions to minimum required scope
- Review and audit secret access regularly

## Testing the Setup

After configuring all secrets and variables:

1. Push to `develop` branch to test staging deployment
2. Create a pull request to `main` to test production deployment
3. Check the Actions tab for workflow execution status
4. Verify deployments in Netlify, Railway, and other services

## Troubleshooting

If deployments fail:

1. Check the Actions tab for error messages
2. Verify all required secrets are set
3. Ensure secret values are correct (no extra spaces, correct format)
4. Check service-specific dashboards for additional error details
5. Review the workflow logs for specific error messages
