name: Deploy

on:
  push:
    branches: [main, develop]
  workflow_run:
    workflows: ["CI/CD Pipeline"]
    types: [completed]
    branches: [main, develop]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: "18"
  PYTHON_VERSION: "3.11"

jobs:
  # Job 1: Deploy to Staging (develop branch)
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' && github.event.workflow_run.conclusion == 'success' && secrets.NETLIFY_AUTH_TOKEN != ''
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build Admin Panel
        run: pnpm build --filter=admin-panel
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.STAGING_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.STAGING_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.STAGING_ADMIN_URL }}

      - name: Install Netlify CLI
        run: npm install -g netlify-cli

      - name: Deploy Admin Panel to Netlify
        run: netlify deploy --dir=apps/admin-panel/out --site=${{ secrets.NETLIFY_ADMIN_SITE_ID || 'placeholder' }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}

      - name: Build Wholesale Portal
        run: pnpm build --filter=wholesale-portal
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.STAGING_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.STAGING_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.STAGING_WHOLESALE_URL }}

      - name: Deploy Wholesale Portal to Netlify
        run: netlify deploy --dir=apps/wholesale-portal/out --site=${{ secrets.NETLIFY_WHOLESALE_SITE_ID || 'placeholder' }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}

      - name: Deploy AI Backend to Railway
        if: ${{ secrets.RAILWAY_TOKEN != '' }}
        run: |
          echo "Railway deployment would happen here"
          echo "Install Railway CLI and deploy when secrets are configured"
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
          RAILWAY_PROJECT_ID: ${{ secrets.RAILWAY_PROJECT_ID }}

  # Job 2: Deploy to Production (main branch)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event.workflow_run.conclusion == 'success' && secrets.NETLIFY_AUTH_TOKEN != ''
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build Admin Panel
        run: pnpm build --filter=admin-panel
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.PROD_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.PROD_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.PROD_ADMIN_URL }}

      - name: Install Netlify CLI
        run: npm install -g netlify-cli

      - name: Deploy Admin Panel to Netlify (Production)
        run: netlify deploy --prod --dir=apps/admin-panel/out --site=${{ secrets.NETLIFY_ADMIN_SITE_ID || 'placeholder' }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}

      - name: Build Wholesale Portal
        run: pnpm build --filter=wholesale-portal
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.PROD_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.PROD_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.PROD_WHOLESALE_URL }}

      - name: Deploy Wholesale Portal to Netlify (Production)
        run: netlify deploy --prod --dir=apps/wholesale-portal/out --site=${{ secrets.NETLIFY_WHOLESALE_SITE_ID || 'placeholder' }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}

      - name: Deploy AI Backend to Railway (Production)
        if: ${{ secrets.RAILWAY_TOKEN != '' }}
        run: |
          echo "Railway production deployment would happen here"
          echo "Install Railway CLI and deploy when secrets are configured"
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
          RAILWAY_PROJECT_ID: ${{ secrets.RAILWAY_PROJECT_ID }}

      - name: Run Database Migrations
        if: ${{ secrets.SUPABASE_ACCESS_TOKEN != '' }}
        run: |
          echo "Database migrations would run here when Supabase is configured"
          echo "npx supabase db push --db-url [DATABASE_URL]"
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Notify Deployment Success
        uses: 8398a7/action-slack@v3
        if: success() && secrets.SLACK_WEBHOOK_URL != ''
        with:
          status: success
          text: "🚀 NutriPro Production Deployment Successful!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Deployment Failure
        uses: 8398a7/action-slack@v3
        if: failure() && secrets.SLACK_WEBHOOK_URL != ''
        with:
          status: failure
          text: "❌ NutriPro Production Deployment Failed!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Job 3: Database Backup
  database-backup:
    name: Database Backup
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && secrets.PROD_DATABASE_URL != ''
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PostgreSQL Client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client

      - name: Create Database Backup
        run: |
          pg_dump ${{ secrets.PROD_DATABASE_URL }} > backup_$(date +%Y%m%d_%H%M%S).sql
        env:
          PGPASSWORD: ${{ secrets.DB_PASSWORD }}

      - name: Upload Backup to S3
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Sync backup to S3
        run: |
          aws s3 cp backup_$(date +%Y%m%d_%H%M%S).sql s3://${{ secrets.BACKUP_BUCKET }}/database/
