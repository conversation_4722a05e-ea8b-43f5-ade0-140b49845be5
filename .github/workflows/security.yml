name: Security Scans

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Job 1: Dependency Vulnerability Scan
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run npm audit
        run: pnpm audit --audit-level moderate
        continue-on-error: true

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        if: ${{ vars.SNYK_ENABLED == 'true' }}
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium
        continue-on-error: true

  # Job 2: Secret Scanning
  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  # Job 3: Code Quality & Security Analysis
  codeql-analysis:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      fail-fast: false
      matrix:
        language: ['javascript', 'python']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality

      - name: Setup Node.js (for JavaScript analysis)
        if: matrix.language == 'javascript'
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: 'npm'

      - name: Setup pnpm (for JavaScript analysis)
        if: matrix.language == 'javascript'
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies (for JavaScript analysis)
        if: matrix.language == 'javascript'
        run: pnpm install --frozen-lockfile

      - name: Setup Python (for Python analysis)
        if: matrix.language == 'python'
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install Python dependencies
        if: matrix.language == 'python'
        run: |
          cd apps/ai-backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{matrix.language}}"

  # Job 4: Container Security Scan (for AI Backend)
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker image
        run: |
          cd apps/ai-backend
          docker build -t nutripro-ai-backend:latest .

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'nutripro-ai-backend:latest'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Job 5: License Compliance Check
  license-check:
    name: License Compliance
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Check licenses
        run: |
          npx license-checker --summary --excludePrivatePackages
          npx license-checker --failOn 'GPL;AGPL;LGPL;UNLICENSED' --excludePrivatePackages

  # Job 6: Security Notification
  security-notification:
    name: Security Notification
    runs-on: ubuntu-latest
    needs: [dependency-scan, secret-scan, codeql-analysis, container-scan, license-check]
    if: failure()
    steps:
      - name: Notify security issues
        uses: 8398a7/action-slack@v3
        if: ${{ vars.SLACK_NOTIFICATIONS_ENABLED == 'true' }}
        with:
          status: failure
          text: "🚨 Security scan failed for NutriPro! Please review the security tab."
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
