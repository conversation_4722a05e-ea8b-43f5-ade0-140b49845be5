name: CI - Core Quality Checks

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: "18"
  PNPM_VERSION: "8"

jobs:
  # Job 1: Code Quality & Linting
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run ESLint
        run: pnpm lint
        continue-on-error: true

      - name: Run Prettier check
        run: pnpm format:check
        continue-on-error: true

      - name: Run TypeScript check
        run: pnpm type-check

      - name: Check for security vulnerabilities
        run: pnpm audit --audit-level moderate
        continue-on-error: true

  # Job 2: Build Test
  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    needs: quality
    strategy:
      matrix:
        app: [admin-panel, wholesale-portal]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build ${{ matrix.app }}
        run: pnpm build --filter=${{ matrix.app }}
        env:
          NEXT_PUBLIC_SUPABASE_URL: "https://placeholder.supabase.co"
          NEXT_PUBLIC_SUPABASE_ANON_KEY: "placeholder-key"

  # Job 3: Success Notification
  success:
    name: CI Success
    runs-on: ubuntu-latest
    needs: [quality, build-test]
    if: success()
    steps:
      - name: Success message
        run: |
          echo "✅ All CI checks passed successfully!"
          echo "🚀 Ready for deployment when secrets are configured"
